<template>
  <div class="worktype-auto-complete">
    <a-auto-complete
      v-model:value="inputValue"
      :options="options"
      :placeholder="placeholder"
      :disabled="disabled"
      @search="handleSearch"
      @select="handleSelect"
      @change="handleChange"
      allowClear
      :filterOption="false"
      :getPopupContainer="getPopupContainer"
      :dropdownMatchSelectWidth="dropdownMatchSelectWidth"
    >
      <template #option="{ value, label, helpChar, useCount, isNew }">
        <div class="option-item">
          <div class="option-main">
            <span class="option-text">{{ label }}</span>
            <span v-if="helpChar" class="help-char">{{ helpChar }}</span>
            <span v-if="isNew" class="new-tag">新建</span>
          </div>
          <div class="option-meta">
            <span v-if="useCount > 0" class="use-count">使用{{ useCount }}次</span>
            <span v-else-if="!isNew" class="use-count">未使用</span>
          </div>
        </div>
      </template>
    </a-auto-complete>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import { debounce } from 'lodash-es';
import { message } from 'ant-design-vue';
import { autoComplete, updateUseCount, autoCreate } from '@/views/occu/ZyWorktype.api';

interface OptionItem {
  value: string;
  label: string;
  helpChar?: string;
  useCount?: number;
  isNew?: boolean;
  raw?: any;
}

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入工种名称'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 10
  },
  dropdownMatchSelectWidth: {
    type: Boolean,
    default: true
  },
  // 是否允许自动创建新工种
  allowAutoCreate: {
    type: Boolean,
    default: true
  },
  // 搜索类型：'name' | 'helpChar' | 'both'
  searchType: {
    type: String,
    default: 'both'
  }
});

const emit = defineEmits(['update:value', 'change', 'select', 'create']);

const inputValue = ref(props.value);
const options = ref<OptionItem[]>([]);
const loading = ref(false);

// 监听外部value变化
watch(() => props.value, (newVal) => {
  inputValue.value = newVal;
});

// 监听内部value变化
watch(inputValue, (newVal) => {
  emit('update:value', newVal);
});

// 防抖搜索函数
const debouncedSearch = debounce(async (keyword: string) => {
  if (!keyword || keyword.length < 1) {
    // 如果没有关键词，显示热门选项
    await loadPopularOptions();
    return;
  }

  try {
    loading.value = true;
    const response = await autoComplete({
      keyword: keyword,
      limit: props.limit,
      searchType: props.searchType
    });

    if (response.success) {
      const records = response.result || [];
      
      // 转换为选项格式
      const newOptions = records.map((item: any) => ({
        value: item.name,
        label: item.name,
        helpChar: item.helpChar,
        useCount: item.useCount || 0,
        isNew: false,
        raw: item
      }));

      // 如果允许自动创建且没有完全匹配的结果，添加"新建"选项
      if (props.allowAutoCreate && keyword.trim()) {
        const exactMatch = records.find((item: any) => 
          item.name.toLowerCase() === keyword.toLowerCase()
        );
        
        if (!exactMatch) {
          newOptions.unshift({
            value: keyword.trim(),
            label: keyword.trim(),
            helpChar: '',
            useCount: 0,
            isNew: true,
            raw: null
          });
        }
      }

      options.value = newOptions;
    }
  } catch (error) {
    console.error('搜索工种失败:', error);
    message.error('搜索工种失败');
  } finally {
    loading.value = false;
  }
}, 300);

// 加载热门选项
const loadPopularOptions = async () => {
  try {
    loading.value = true;
    const response = await autoComplete({
      keyword: '',
      limit: props.limit,
      searchType: 'popular' // 获取热门工种
    });

    if (response.success) {
      const records = response.result || [];
      options.value = records.map((item: any) => ({
        value: item.name,
        label: item.name,
        helpChar: item.helpChar,
        useCount: item.useCount || 0,
        isNew: false,
        raw: item
      }));
    }
  } catch (error) {
    console.error('加载热门工种失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (value: string) => {
  debouncedSearch(value);
};

// 处理选择
const handleSelect = async (value: string, option: any) => {
  inputValue.value = value;
  
  if (option.isNew) {
    // 自动创建新工种
    try {
      const response = await autoCreate({
        name: value,
        helpChar: '', // 可以后续让用户补充
        enableFlag: 1,
        sort: 999
      });
      
      if (response.success) {
        message.success(`工种"${value}"已自动创建`);
        emit('create', response.result);
      }
    } catch (error) {
      console.error('自动创建工种失败:', error);
      message.error('自动创建工种失败');
    }
  } else if (option.raw) {
    // 更新使用频次
    try {
      await updateUseCount({ id: option.raw.id });
    } catch (error) {
      console.error('更新使用频次失败:', error);
    }
  }
  
  emit('select', value, option);
};

// 处理输入变化
const handleChange = (value: string) => {
  emit('change', value);
};

// 获取弹出容器
const getPopupContainer = (triggerNode: HTMLElement) => {
  return triggerNode.parentNode as HTMLElement;
};

// 初始化时加载热门选项
loadPopularOptions();
</script>

<style lang="less" scoped>
.worktype-auto-complete {
  width: 100%;

  :deep(.ant-select-dropdown) {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  :deep(.ant-select-item) {
    padding: 8px 12px;
    border-radius: 4px;
    margin: 2px 4px;
    transition: all 0.2s ease;

    &:hover {
      background: #f0f8ff;
      transform: translateY(-1px);
    }

    &.ant-select-item-option-selected {
      background: #e6f7ff;
      border: 1px solid #91d5ff;
    }
  }

  .option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    min-height: 32px;

    .option-main {
      display: flex;
      align-items: center;
      flex: 1;

      .option-text {
        font-weight: 500;
        color: #333;
        font-size: 14px;
      }

      .help-char {
        margin-left: 8px;
        padding: 2px 8px;
        background: linear-gradient(135deg, #f0f0f0, #e8e8e8);
        border-radius: 12px;
        font-size: 11px;
        color: #666;
        font-weight: 500;
        border: 1px solid #d9d9d9;
      }

      .new-tag {
        margin-left: 8px;
        padding: 2px 8px;
        background: linear-gradient(135deg, #52c41a, #389e0d);
        color: white;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
        animation: pulse 2s infinite;
      }
    }

    .option-meta {
      .use-count {
        font-size: 11px;
        color: #999;
        background: #fafafa;
        padding: 2px 6px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
      }
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.5);
  }
  100% {
    box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
  }
}
</style>
