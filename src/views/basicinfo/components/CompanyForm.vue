<template>
  <a-spin :spinning="confirmLoading">
    <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row>
        <a-col :span="12">
          <a-form-item label="类型" v-bind="validateInfos.orgType">
            <j-dict-select-tag type="radio" v-model:value="formData.orgType" dictCode="org_type" placeholder="请选择类型" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="父级节点" v-bind="validateInfos.pid">
            <j-tree-select
              placeholder="请选择上级单位"
              v-model:value="formData.pid"
              dict="company,name,id"
              pidField="pid"
              pidValue="0"
              hasChildField="has_child"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="名称" v-bind="validateInfos.name">
            <a-input v-model:value="formData.name" placeholder="请输入名称" :disabled="disabled" @change="setHelpChar" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="简称" v-bind="validateInfos.shortName">
            <a-input v-model:value="formData.shortName" placeholder="请输入简称" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="助记码" v-bind="validateInfos.helpChar">
            <a-input v-model:value="formData.helpChar" placeholder="请输入助记码" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="来源" v-bind="validateInfos.source">
            <j-dict-select-tag v-model:value="formData.source" dictCode="customer_source" placeholder="请选择来源" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系电话" v-bind="validateInfos.telephone">
            <a-input v-model:value="formData.telephone" placeholder="请输入联系电话" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="合同性质" v-bind="validateInfos.contractType">
            <j-dict-select-tag v-model:value="formData.contractType" dictCode="contract_type" placeholder="请选择合同性质" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="省市区县" v-bind="validateInfos.pcca">
            <p v-if="formData.addressDetail && !showAddrComp"
              ><span>{{ formData.addressDetail }}</span>
              <a @click="formData = true" style="margin-left: 10px">更换</a>
            </p>
            <pcasv @change="handleArea" :level="4" v-else />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="详细地址" v-bind="validateInfos.address">
            <a-textarea v-model:value="formData.address" :rows="4" placeholder="请输入详细地址" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="单位分类" v-bind="validateInfos.categoryId">
            <j-dict-select-tag
              v-model:value="formData.categoryId"
              dictCode="company_category,name,id"
              placeholder="请选择单位分类"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="包含职业体检" v-bind="validateInfos.occupationalExFlag">
            <j-switch v-model:value="formData.occupationalExFlag" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-divider orientation="left"><a-typography-title :level="5">职业体检信息</a-typography-title></a-divider>

        <a-col :span="12">
          <a-form-item label="用工单位名称" v-bind="validateInfos.workCom" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.workCom" placeholder="请输入用工单位名称" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="统一社会信用代码" v-bind="validateInfos.creditCode" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.creditCode" placeholder="请输入统一社会信用代码" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <!--        <a-col :span="12">-->
        <!--          <a-form-item label="用工信用代码" v-bind="validateInfos.workCreditCard">-->
        <!--            <a-input v-model:value="formData.workCreditCard" placeholder="请输入用工信用代码" :disabled="disabled" />-->
        <!--          </a-form-item>-->
        <!--        </a-col>-->
        <a-col :span="12">
          <a-form-item label="经济类型" v-bind="validateInfos.workEcoType" :required="formData.occupationalExFlag == 1">
            <j-tree-select dict="zy_eco_type,name,id" pidValue="0" :disabled="disabled" v-model:value="formData.workEcoType" size="middle" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="所在区全名" v-bind="validateInfos.areaName" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.areaName" placeholder="请输入所在区全名" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="职工人数" v-bind="validateInfos.employeeCount" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.employeeCount" placeholder="请输入职工人数" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="接触职业病危害因素人数" v-bind="validateInfos.riskEmployeeCount" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.riskEmployeeCount" placeholder="请输入接触职业病危害因素人数" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="用工企业规模" v-bind="validateInfos.workEnSize" :required="formData.occupationalExFlag == 1">
            <j-dict-select-tag v-model:value="formData.workEnSize" dictCode="workEnSize" placeholder="请选择用工企业规模" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="用工地区编码" v-bind="validateInfos.workAreaCode" :required="formData.occupationalExFlag == 1">
            <j-async-search-select
              size="middle"
              placeholder="用工地区编码"
              v-model:value="formData.workAreaCode"
              dict="zy_pcca_dict,name,code"
              :enableHelpCharSearch="false"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="用工行业" v-bind="validateInfos.workIndustry" :required="formData.occupationalExFlag == 1">
            <j-tree-select
              dict="zy_industry,name,id"
              condition='{"del_flag":"0"}'
              pidValue="0"
              :disabled="disabled"
              v-model:value="formData.workIndustry"
              size="middle"
            />
          </a-form-item>
        </a-col>
        <!--        <a-col :span="12">
          <a-form-item label="添加时间" v-bind="validateInfos.creatTime">
            <a-date-picker
              placeholder="请选择添加时间"
              v-model:value="formData.creatTime"
              showTime
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              disabled
            />
          </a-form-item>
        </a-col>-->
        <a-col :span="12">
          <a-form-item label="填表单位名称" v-bind="validateInfos.formCom" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.formCom" placeholder="请输入填表单位名称" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="填表人" v-bind="validateInfos.formUser" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.formUser" placeholder="请输入填表人" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="报告时间" v-bind="validateInfos.reportTime">
            <a-date-picker
              placeholder="请选择报告时间"
              v-model:value="formData.reportTime"
              showTime
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="填表人电话" v-bind="validateInfos.formPhone" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.formPhone" placeholder="请输入填表人电话" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="报告单位名称" v-bind="validateInfos.reportCom" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.reportCom" placeholder="请输入报告单位名称" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="报告人姓名" v-bind="validateInfos.reporter" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.reporter" placeholder="请输入报告人姓名" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="填表日期" v-bind="validateInfos.formTime" :required="formData.occupationalExFlag == 1">
            <a-date-picker
              placeholder="请选择填表日期"
              v-model:value="formData.formTime"
              showTime
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="报告人电话" v-bind="validateInfos.reporterPhone" :required="formData.occupationalExFlag == 1">
            <a-input v-model:value="formData.reporterPhone" placeholder="请输入报告人电话" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import { computed, defineExpose, defineProps, nextTick, reactive, ref, unref, watch } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import JTreeSelect from '/@/components/Form/src/jeecg/components/JTreeSelect.vue';
  import { getValueType } from '/@/utils';
  import { loadTreeData, saveOrUpdateDict } from '../Company.api';
  import { Form } from 'ant-design-vue';
  import { duplicateValidate } from '/@/utils/helper/validator';
  import { JAsyncSearchSelect } from '@/components/Form';
  import Pcasv from '@/components/pcca/Pcasv.vue';

  const { createErrorModal } = useMessage();
  const useForm = Form.useForm;
  const formRef = ref();
  const isUpdate = ref(true);
  const expandedRowKeys = ref([]);
  const treeData = ref([]);
  const pidField = ref<string>('pid');
  const emit = defineEmits(['register', 'ok']);
  let model: Nullable<Recordable> = null;
  const formData = reactive<Record<string, any>>({
    id: '',
    orgType: '公司',
    pid: '',
    name: '',
    shortName: '',
    helpChar: '',
    source: '',
    telephone: '',
    contractType: '',
    pcca: '',
    address: '',
    categoryId: '',
    indutry: '',
    economicType: '',
    occupationalExFlag: 0,
    creditCode: '',
    enSize: '',
    formCom: '',
    formUser: '',
    reportTime: '',
    formPhone: '',
    reportCom: '',
    reporter: '',
    formTime: '',
    reporterPhone: '',
    areaName: '',
    workCom: '',
    workEcoType: '',
    workCreditCard: '',
    workEnSize: '',
    workAreaCode: '',
    workIndustry: '',
    creatTime: '',
    addressDetail: '',
    riskEmployeeCount: 0,
    employeeCount: 0,
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 8 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = {
    orgType: [{ required: true, message: '请输入类型!' }],
    name: [{ required: true, message: '请输入名称!' }, { validator: nameDuplicatevalidate }],
    // 职业检相关字段 - 动态校验
    workCom: [],
    workEcoType: [],
    workCreditCard: [],
    workEnSize: [],
    workAreaCode: [],
    workIndustry: [],
    creditCode: [],
    enSize: [],
    areaName: [],
    employeeCount: [],
    riskEmployeeCount: [],
    // 职业检报告相关字段 - 动态校验
    formCom: [],
    formUser: [],
    reportTime: [],
    formPhone: [],
    reportCom: [],
    reporter: [],
    formTime: [],
    reporterPhone: [],
  };
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => {} },
    formBpm: { type: Boolean, default: true },
  });

  watch(
    () => formData.occupationalExFlag,
    (newVal) => {
      if (newVal == 1) {
        // 职业检基本信息字段必填
        validatorRules.workCom = [{ required: true, message: '请输入用工单位名称!' }];
        validatorRules.workEcoType = [{ required: true, message: '请选择经济类型!' }];
        validatorRules.workEnSize = [{ required: true, message: '请选择用工企业规模!' }];
        validatorRules.workAreaCode = [{ required: true, message: '请选择用工地区编码!' }];
        validatorRules.workIndustry = [{ required: true, message: '请选择用工行业!' }];
        validatorRules.creditCode = [{ required: true, message: '请输入统一社会信用代码!' }];
        validatorRules.areaName = [{ required: true, message: '请输入所在区全名!' }];
        validatorRules.employeeCount = [{ required: true, message: '请输入职工人数!' }];
        validatorRules.riskEmployeeCount = [{ required: true, message: '请输入接触职业病危害因素人数!' }];

        // 职业检报告相关字段必填
        validatorRules.formCom = [{ required: true, message: '请输入填表单位名称!' }];
        validatorRules.formUser = [{ required: true, message: '请输入填表人!' }];
        //validatorRules.reportTime = [{ required: true, message: '请选择报告时间!' }];
        validatorRules.formPhone = [{ required: true, message: '请输入填表人电话!' }];
        validatorRules.reportCom = [{ required: true, message: '请输入报告单位名称!' }];
        validatorRules.reporter = [{ required: true, message: '请输入报告人姓名!' }];
        validatorRules.formTime = [{ required: true, message: '请选择填表日期!' }];
        validatorRules.reporterPhone = [{ required: true, message: '请输入报告人电话!' }];
      } else {
        // 清空职业检基本信息字段校验
        validatorRules.workCom = [];
        validatorRules.workEcoType = [];
        validatorRules.workEnSize = [];
        validatorRules.workAreaCode = [];
        validatorRules.workIndustry = [];
        validatorRules.creditCode = [];
        validatorRules.areaName = [];
        validatorRules.employeeCount = [];
        validatorRules.riskEmployeeCount = [];

        // 清空职业检报告相关字段校验
        validatorRules.formCom = [];
        validatorRules.formUser = [];
        //validatorRules.reportTime = [];
        validatorRules.formPhone = [];
        validatorRules.reportCom = [];
        validatorRules.reporter = [];
        validatorRules.formTime = [];
        validatorRules.reporterPhone = [];
      }
    }
  );

  const showAddrComp = ref<boolean>(false);
  function handleArea(data) {
    console.log('HealthRecordForm.vue handleArea', data);
    formData.province = data[0]?.name;
    formData.provinceCode = data[0]?.code;
    formData.city = data[1]?.name;
    formData.cityCode = data[1]?.code;
    formData.area = data[2]?.name;
    formData.areaCode = data[2]?.code;
    formData.street = data[3]?.name;
    formData.streetCode = data[3]?.code;
    formData.addressDetail = `${formData.province ?? ''}${formData.city ?? ''}${formData.area ?? ''}${formData.street ?? ''}`;
  }

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  function setHelpChar() {
    /* if (formData.name) {
      formData.helpChar = PinyinUtil.getInitials(formData.name);
    }*/
    formData.workCom = formData.name;
    formData.formCom = formData.name;
  }

  /**
   * 新增
   */
  function add(obj = {}) {
    edit(obj);
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(async () => {
      resetFields();
      expandedRowKeys.value = [];
      treeData.value = await loadTreeData({ async: false, pcode: '' });
      //赋值
      Object.assign(formData, record);
      model = record;
    });
  }

  /**
   * 根据pid获取展开的节点
   * @param pid
   * @param arr
   */
  function getExpandKeysByPid(pid, arr) {
    if (pid && arr && arr.length > 0) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].key == pid && unref(expandedRowKeys).indexOf(pid) < 0) {
          expandedRowKeys.value.push(arr[i].key);
          getExpandKeysByPid(arr[i]['parentId'], unref(treeData));
        } else {
          getExpandKeysByPid(pid, arr[i].children);
        }
      }
    }
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    try {
      await validate();
    } catch (e) {
      const errorMessages = e.errorFields
        .map((field) => {
          return `${field.errors.join('')}`;
        })
        .join('<br/>');

      createErrorModal({
        title: '表单验证失败',
        content: `${errorMessages}`,
      });

      //console.log(e);
      //给出更详细的提示，能够提示出哪个字段的错误
      //message.error('请检查表单是否填写完整！');
      return;
    }

    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    if (formData.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in formData) {
      //如果该数据是数组并且是字符串类型
      if (formData[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          formData[data] = formData[data].join(',');
        }
      }
    }
    await saveOrUpdateDict(formData, isUpdate.value)
      .then(async (res) => {
        if (res.success) {
          await getExpandKeysByPid(formData['pid'], unref(treeData));
          delete formData['children'];
          emit('ok', {
            isUpdate: unref(isUpdate),
            values: { ...formData },
            expandedArr: unref(expandedRowKeys).reverse(),
            // 是否更改了父级节点
            changeParent: model != null && model['pid'] != formData['pid'],
          });
          createMessage.success(res.message);
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  /**
   * 值改变事件触发
   * @param key
   * @param value
   */
  function handleFormChange(key, value) {
    formData[key] = value;
  }

  async function nameDuplicatevalidate(_r, value) {
    return duplicateValidate('company', 'name', value, formData.id || '');
  }
  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    height: 500px !important;
    overflow-y: auto;
    padding: 14px;
  }
</style>
