-- 工种自动完成组件数据库更新脚本
-- 为zy_worktype表添加使用频次字段和相关索引

-- 1. 为zy_worktype表添加use_count字段
ALTER TABLE zy_worktype ADD COLUMN use_count INT DEFAULT 0 COMMENT '使用频次';

-- 2. 为现有数据初始化use_count字段
UPDATE zy_worktype SET use_count = 0 WHERE use_count IS NULL;

-- 3. 创建索引优化查询性能
-- 为name字段创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_zy_worktype_name ON zy_worktype(name);

-- 为helpChar字段创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_zy_worktype_help_char ON zy_worktype(help_char);

-- 为use_count字段创建索引
CREATE INDEX IF NOT EXISTS idx_zy_worktype_use_count ON zy_worktype(use_count DESC);

-- 为enableFlag字段创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_zy_worktype_enable_flag ON zy_worktype(enable_flag);

-- 创建复合索引用于自动完成搜索
CREATE INDEX IF NOT EXISTS idx_zy_worktype_search ON zy_worktype(enable_flag, use_count DESC, name, help_char);

-- 4. 创建工种缓存表
CREATE TABLE IF NOT EXISTS zy_worktype_cache (
    id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '主键ID',
    cache_key VARCHAR(100) NOT NULL COMMENT '缓存键',
    cache_data TEXT COMMENT '缓存数据(JSON格式)',
    expire_time DATETIME COMMENT '过期时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='工种缓存表';

-- 为缓存表创建索引
CREATE INDEX IF NOT EXISTS idx_zy_worktype_cache_key ON zy_worktype_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_zy_worktype_cache_expire ON zy_worktype_cache(expire_time);

-- 5. 插入一些测试数据（可选）
INSERT IGNORE INTO zy_worktype (id, name, code, help_char, enable_flag, sort, use_count) VALUES
('test001', '电工', 'DG001', 'DG', 1, 1, 15),
('test002', '焊工', 'HG001', 'HG', 1, 2, 12),
('test003', '车工', 'CG001', 'CG', 1, 3, 8),
('test004', '钳工', 'QG001', 'QG', 1, 4, 6),
('test005', '铣工', 'XG001', 'XG', 1, 5, 4),
('test006', '磨工', 'MG001', 'MG', 1, 6, 3),
('test007', '装配工', 'ZPG001', 'ZPG', 1, 7, 10),
('test008', '检验工', 'JYG001', 'JYG', 1, 8, 7),
('test009', '维修工', 'WXG001', 'WXG', 1, 9, 9),
('test010', '操作工', 'CZG001', 'CZG', 1, 10, 5);

-- 6. 验证数据
SELECT 
    name,
    code,
    help_char,
    enable_flag,
    use_count,
    sort
FROM zy_worktype 
WHERE enable_flag = 1 
ORDER BY use_count DESC, sort ASC 
LIMIT 10;

-- 7. 查看索引创建情况
SHOW INDEX FROM zy_worktype;
SHOW INDEX FROM zy_worktype_cache;
